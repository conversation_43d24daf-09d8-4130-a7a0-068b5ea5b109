package main

import (
	"fmt"
	"strings"
)

// generateTypePatterns 生成基于question_type的题目类型模式
func generateTypePatterns(questionType string) []string {
	if questionType == "" {
		return []string{}
	}
	
	patterns := []string{}
	
	// 完整的题目类型模式（按优先级排序）
	patterns = append(patterns, []string{
		"(" + questionType + ")",     // (判断题)
		"（" + questionType + "）",    // （判断题）
		"(" + questionType,           // (判断题
		"（" + questionType,          // （判断题
		questionType + ")",           // 判断题)
		questionType + "）",          // 判断题）
		questionType,                 // 判断题
	}...)
	
	// 处理不完整的题目类型（如"断题"代替"判断题"）
	if len(questionType) > 1 {
		incompleteType := questionType[1:] // 如"判断题" -> "断题"
		patterns = append(patterns, []string{
			incompleteType + ")",         // 断题)
			incompleteType + "）",        // 断题）
		}...)
	}
	
	// 只有"题"字的情况
	patterns = append(patterns, []string{
		"题)",                        // 题)
		"题）",                       // 题）
	}...)
	
	return patterns
}

func main() {
	// 测试"断题)"的匹配
	questionType := "判断题"
	text := "断题)08、等17分钟后如果没问题就可以走了。"
	
	fmt.Printf("测试文本: %s\n", text)
	fmt.Printf("题目类型: %s\n", questionType)
	
	patterns := generateTypePatterns(questionType)
	fmt.Printf("生成的模式: %v\n", patterns)
	
	// 测试每个模式
	for i, pattern := range patterns {
		if strings.HasPrefix(text, pattern) {
			fmt.Printf("模式 %d '%s' 匹配成功!\n", i+1, pattern)
			result := strings.TrimPrefix(text, pattern)
			fmt.Printf("清洗后结果: %s\n", result)
			return
		} else {
			fmt.Printf("模式 %d '%s' 不匹配\n", i+1, pattern)
		}
	}
	
	fmt.Println("没有找到匹配的模式")
}
