package main

import (
	"fmt"
	"strings"
)

// cleanQuestionNumber 清洗序号部分
func cleanQuestionNumber(text, questionNum string) string {
	if questionNum == "" {
		return text
	}
	
	// 生成序号的各种模式
	numberPatterns := generateNumberPatterns(questionNum)
	
	// 尝试匹配并清除序号
	for _, pattern := range numberPatterns {
		if strings.HasPrefix(text, pattern) {
			return strings.TrimPrefix(text, pattern)
		}
	}
	
	return text
}

// cleanQuestionType 清洗题目类型部分
func cleanQuestionType(text, questionType string) string {
	if questionType == "" {
		return text
	}
	
	// 生成题目类型的各种模式
	typePatterns := generateTypePatterns(questionType)
	
	// 尝试匹配并清除题目类型
	for _, pattern := range typePatterns {
		if strings.HasPrefix(text, pattern) {
			return strings.TrimPrefix(text, pattern)
		}
	}
	
	return text
}

// generateNumberPatterns 生成基于question_num的序号模式
func generateNumberPatterns(questionNum string) []string {
	if questionNum == "" {
		return []string{}
	}
	
	patterns := []string{}
	
	// 原始序号模式
	patterns = append(patterns, []string{
		questionNum + ".",
		questionNum + "、",
		questionNum + "。",
		questionNum + " ",
		questionNum + "  ", // 两个空格的情况
	}...)
	
	// 处理带前导零的情况（如"8" -> "08"）
	if len(questionNum) == 1 {
		paddedNum := "0" + questionNum
		patterns = append(patterns, []string{
			paddedNum + ".",
			paddedNum + "、",
			paddedNum + "。",
			paddedNum + " ",
			paddedNum + "  ",
		}...)
	}
	
	// 处理去掉前导零的情况（如"08" -> "8"）
	if len(questionNum) == 2 && questionNum[0] == '0' {
		trimmedNum := questionNum[1:]
		patterns = append(patterns, []string{
			trimmedNum + ".",
			trimmedNum + "、",
			trimmedNum + "。",
			trimmedNum + " ",
			trimmedNum + "  ",
		}...)
	}
	
	return patterns
}

// generateTypePatterns 生成基于question_type的题目类型模式
func generateTypePatterns(questionType string) []string {
	if questionType == "" {
		return []string{}
	}
	
	patterns := []string{}
	
	// 完整的题目类型模式（按优先级排序）
	patterns = append(patterns, []string{
		"(" + questionType + ")",     // (判断题)
		"（" + questionType + "）",    // （判断题）
		"(" + questionType,           // (判断题
		"（" + questionType,          // （判断题
		questionType + ")",           // 判断题)
		questionType + "）",          // 判断题）
		questionType,                 // 判断题
	}...)
	
	// 处理不完整的题目类型（如"判断题" -> "断题"）
	if len(questionType) > 1 {
		incompleteType := questionType[1:] // 如"判断题" -> "断题"
		patterns = append(patterns, []string{
			incompleteType + ")",         // 断题)
			incompleteType + "）",        // 断题）
		}...)
	}
	
	// 只有"题"字的情况
	patterns = append(patterns, []string{
		"题)",                        // 题)
		"题）",                       // 题）
	}...)
	
	return patterns
}

// CleanQuestionText 主清洗函数
func CleanQuestionText(questionText, questionType, questionNum string) string {
	if questionText == "" {
		return ""
	}

	cleaned := questionText

	// 第一步：清洗题目类型部分
	cleaned = cleanQuestionType(cleaned, questionType)

	// 第二步：清洗序号部分
	cleaned = cleanQuestionNumber(cleaned, questionNum)

	// 返回清洗后的结果
	return strings.TrimSpace(cleaned)
}

func main() {
	// 测试用例
	testCases := []struct {
		desc         string
		questionText string
		questionType string
		questionNum  string
		expected     string
	}{
		{
			desc:         "完整格式：(判断题)08、",
			questionText: "(判断题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "不完整类型：断题)08、",
			questionText: "断题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "仅序号：08.",
			questionText: "08.等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "仅题字：题)08、",
			questionText: "题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "正常题目（无需清洗）",
			questionText: "雾天跟车行驶,应如何安全驾驶?",
			questionType: "多选题",
			questionNum:  "15",
			expected:     "雾天跟车行驶,应如何安全驾驶?",
		},
	}

	fmt.Println("最终清洗逻辑测试结果:")
	fmt.Println(strings.Repeat("=", 80))
	
	successCount := 0
	totalCount := len(testCases)
	
	for i, testCase := range testCases {
		fmt.Printf("\n测试 %d: %s\n", i+1, testCase.desc)
		fmt.Printf("原文: %s\n", testCase.questionText)
		fmt.Printf("类型: %s, 序号: %s\n", testCase.questionType, testCase.questionNum)
		fmt.Printf("期望: %s\n", testCase.expected)
		
		// 分步骤显示清洗过程
		step1 := cleanQuestionType(testCase.questionText, testCase.questionType)
		fmt.Printf("步骤1(清洗类型): %s\n", step1)

		step2 := cleanQuestionNumber(step1, testCase.questionNum)
		fmt.Printf("步骤2(清洗序号): %s\n", step2)
		
		result := CleanQuestionText(testCase.questionText, testCase.questionType, testCase.questionNum)
		fmt.Printf("最终结果: %s\n", result)
		
		if result == testCase.expected {
			fmt.Printf("✅ 通过\n")
			successCount++
		} else {
			fmt.Printf("❌ 失败\n")
		}
		
		fmt.Println(strings.Repeat("-", 60))
	}
	
	fmt.Printf("\n" + strings.Repeat("=", 80))
	fmt.Printf("\n测试总结: %d/%d 通过 (%.1f%%)\n", successCount, totalCount, float64(successCount)/float64(totalCount)*100)
	
	if successCount == totalCount {
		fmt.Println("🎉 所有测试通过！清洗逻辑工作正常。")
	} else {
		fmt.Printf("⚠️  有 %d 个测试失败，需要进一步优化。\n", totalCount-successCount)
	}
}
