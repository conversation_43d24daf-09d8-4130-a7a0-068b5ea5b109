package main

import (
	"fmt"
	"regexp"
)

func main() {
	// 当前的正则表达式
	currentRegex := `^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`
	
	// 分解分析
	fmt.Println("当前正则表达式详细分析:")
	fmt.Println("完整正则:", currentRegex)
	fmt.Println()
	
	// 测试序号部分
	numberRegex := `(?:[1-9]|1[0-9]|20)`
	fmt.Println("序号部分正则:", numberRegex)
	
	testNumbers := []string{"1", "5", "10", "15", "20", "25"}
	
	regex := regexp.MustCompile(numberRegex)
	
	for _, num := range testNumbers {
		matches := regex.FindAllString(num, -1)
		fmt.Printf("测试 '%s': ", num)
		if len(matches) > 0 {
			fmt.Printf("匹配到 '%s'\n", matches[0])
		} else {
			fmt.Printf("未匹配\n")
		}
	}
	
	fmt.Println()
	fmt.Println("问题分析:")
	fmt.Println("对于 '15':")
	fmt.Println("- [1-9] 可以匹配 '1'")
	fmt.Println("- 1[0-9] 可以匹配 '15'")
	fmt.Println("- 但正则引擎会选择第一个匹配的选项 [1-9]，只匹配 '1'")
	
	fmt.Println()
	fmt.Println("完整测试:")
	fullRegex := regexp.MustCompile(currentRegex)
	
	testCases := []string{
		"(多选题)15.雾天跟车行驶,应如何安全驾驶?",
		"15.雾天跟车行驶,应如何安全驾驶?",
	}
	
	for _, testCase := range testCases {
		fmt.Printf("原文: %s\n", testCase)
		
		// 查找所有匹配
		allMatches := fullRegex.FindAllStringSubmatch(testCase, -1)
		fmt.Printf("匹配组: %v\n", allMatches)
		
		// 查找匹配的字符串
		match := fullRegex.FindString(testCase)
		fmt.Printf("匹配字符串: '%s'\n", match)
		
		// 替换结果
		result := fullRegex.ReplaceAllString(testCase, "")
		fmt.Printf("替换后: %s\n", result)
		fmt.Println()
	}
}
