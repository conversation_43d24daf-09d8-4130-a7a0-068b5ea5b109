package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库连接配置
	dsn := "gmdns:5e7fFn3HpPfuQ6Qx42Az@tcp(47.96.0.212:3380)/solve_api_go?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("✅ 成功连接到数据库")

	// 查看表结构
	query := "SHOW TABLES"
	rows, err := db.Query(query)
	if err != nil {
		log.Fatalf("Failed to show tables: %v", err)
	}
	defer rows.Close()

	fmt.Println("\n数据库中的表:")
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			log.Printf("Failed to scan table name: %v", err)
			continue
		}
		fmt.Printf("- %s\n", tableName)
	}

	// 查看questions表的记录数
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM questions").Scan(&count)
	if err != nil {
		log.Fatalf("Failed to count questions: %v", err)
	}
	fmt.Printf("\nquestions表总记录数: %d\n", count)

	// 查看有qwen_raw数据的记录数
	var qwenCount int
	err = db.QueryRow("SELECT COUNT(*) FROM questions WHERE qwen_raw IS NOT NULL AND qwen_raw != ''").Scan(&qwenCount)
	if err != nil {
		log.Fatalf("Failed to count qwen_raw records: %v", err)
	}
	fmt.Printf("包含qwen_raw数据的记录数: %d\n", qwenCount)

	// 查看前几条记录的基本信息
	query = `
		SELECT id, question_text, question_type 
		FROM questions 
		WHERE qwen_raw IS NOT NULL AND qwen_raw != ''
		LIMIT 5
	`
	
	rows, err = db.Query(query)
	if err != nil {
		log.Fatalf("Failed to query sample records: %v", err)
	}
	defer rows.Close()

	fmt.Println("\n前5条记录示例:")
	fmt.Println("ID | Question Type | Question Text")
	fmt.Println("---|---------------|---------------")
	
	for rows.Next() {
		var id int64
		var questionText, questionType string
		
		if err := rows.Scan(&id, &questionText, &questionType); err != nil {
			log.Printf("Failed to scan record: %v", err)
			continue
		}
		
		// 截断过长的文本
		if len(questionText) > 50 {
			questionText = questionText[:50] + "..."
		}
		
		fmt.Printf("%d | %s | %s\n", id, questionType, questionText)
	}
}
