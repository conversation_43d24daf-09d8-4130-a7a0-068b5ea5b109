package main

import (
	"fmt"
	"strings"
)

func main() {
	// 测试基本的字符串操作
	questionType := "判断题"
	text := "断题)08、等17分钟后如果没问题就可以走了。"
	
	fmt.Printf("原文: %s\n", text)
	fmt.Printf("题目类型: %s\n", questionType)
	
	// 测试去掉第一个字
	if len(questionType) > 1 {
		incompleteType := questionType[1:] // "断题"
		pattern := incompleteType + ")"    // "断题)"
		
		fmt.Printf("不完整类型: %s\n", incompleteType)
		fmt.Printf("测试模式: %s\n", pattern)
		
		if strings.HasPrefix(text, pattern) {
			fmt.Printf("✅ 模式匹配成功!\n")
			result := strings.TrimPrefix(text, pattern)
			fmt.Printf("清洗后结果: %s\n", result)
		} else {
			fmt.Printf("❌ 模式不匹配\n")
			fmt.Printf("文本开头: %s\n", text[:len(pattern)])
		}
	}
}
