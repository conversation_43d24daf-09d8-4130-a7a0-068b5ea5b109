package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

// APIRequest API请求结构
type APIRequest struct {
	ImageURL string `json:"image_url"`
}

// APIResponse API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// TestResult 测试结果结构
type TestResult struct {
	ImageNumber int           `json:"image_number"`
	ImageURL    string        `json:"image_url"`
	StatusCode  int           `json:"status_code"`
	Success     bool          `json:"success"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error,omitempty"`
	Timestamp   time.Time     `json:"timestamp"`
}

// TestConfig 测试配置
type TestConfig struct {
	BaseURL      string        `json:"base_url"`
	ImageBaseURL string        `json:"image_base_url"`
	StartImage   int           `json:"start_image"`
	EndImage     int           `json:"end_image"`
	Interval     time.Duration `json:"interval"`
	Timeout      time.Duration `json:"timeout"`
}

func main() {
	// 测试配置
	config := TestConfig{
		BaseURL:      "http://localhost:8080/api/v1/process-image",
		ImageBaseURL: "http://solve.igmdns.com/img/",
		StartImage:   1,
		EndImage:     200,
		Interval:     15 * time.Second,
		Timeout:      60 * time.Second,
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: config.Timeout,
	}

	// 存储测试结果
	var results []TestResult

	// 创建日志文件
	logFile, err := os.Create(fmt.Sprintf("api_test_results_%s.log", time.Now().Format("20060102_150405")))
	if err != nil {
		log.Fatal("Failed to create log file:", err)
	}
	defer logFile.Close()

	// 创建日志记录器
	logger := log.New(logFile, "", log.LstdFlags)

	fmt.Printf("开始API测试 - 从图片 %02d 到 %02d，每次间隔 %v\n", config.StartImage, config.EndImage, config.Interval)
	fmt.Printf("API地址: %s\n", config.BaseURL)
	fmt.Printf("图片基础URL: %s\n", config.ImageBaseURL)
	fmt.Println("=" * 60)

	// 记录开始时间
	startTime := time.Now()

	// 循环测试
	for i := config.StartImage; i <= config.EndImage; i++ {
		// 构建图片URL
		imageURL := fmt.Sprintf("%s%02d.jpg", config.ImageBaseURL, i)
		
		// 记录当前测试信息
		fmt.Printf("[%d/%d] 测试图片: %s\n", i, config.EndImage, imageURL)
		logger.Printf("[%d/%d] Testing image: %s", i, config.EndImage, imageURL)

		// 执行API请求
		result := testAPI(client, config.BaseURL, imageURL, i)
		results = append(results, result)

		// 输出结果
		if result.Success {
			fmt.Printf("✅ 成功 - 状态码: %d, 耗时: %v\n", result.StatusCode, result.Duration)
			logger.Printf("✅ SUCCESS - Status: %d, Duration: %v", result.StatusCode, result.Duration)
		} else {
			fmt.Printf("❌ 失败 - 状态码: %d, 错误: %s, 耗时: %v\n", result.StatusCode, result.Error, result.Duration)
			logger.Printf("❌ FAILED - Status: %d, Error: %s, Duration: %v", result.StatusCode, result.Error, result.Duration)
		}

		// 如果不是最后一次请求，等待间隔时间
		if i < config.EndImage {
			fmt.Printf("等待 %v 后继续下一次请求...\n", config.Interval)
			time.Sleep(config.Interval)
		}

		fmt.Println("-" * 40)
	}

	// 计算总耗时
	totalDuration := time.Since(startTime)

	// 生成测试报告
	generateReport(results, totalDuration, logger)

	// 保存结果到JSON文件
	saveResultsToJSON(results, totalDuration)

	fmt.Println("测试完成！详细结果已保存到日志文件和JSON文件中。")
}

// testAPI 执行单次API测试
func testAPI(client *http.Client, apiURL, imageURL string, imageNumber int) TestResult {
	startTime := time.Now()
	
	result := TestResult{
		ImageNumber: imageNumber,
		ImageURL:    imageURL,
		Timestamp:   startTime,
	}

	// 构建请求体
	requestBody := APIRequest{
		ImageURL: imageURL,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to marshal request: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		result.Error = fmt.Sprintf("Failed to create request: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to send request: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	defer resp.Body.Close()

	// 记录响应状态码和耗时
	result.StatusCode = resp.StatusCode
	result.Duration = time.Since(startTime)

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to read response: %v", err)
		return result
	}

	// 检查状态码
	if resp.StatusCode == http.StatusOK {
		result.Success = true
	} else {
		result.Error = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	return result
}

// generateReport 生成测试报告
func generateReport(results []TestResult, totalDuration time.Duration, logger *log.Logger) {
	successCount := 0
	failureCount := 0
	var totalResponseTime time.Duration
	var minDuration, maxDuration time.Duration

	for i, result := range results {
		if result.Success {
			successCount++
		} else {
			failureCount++
		}

		totalResponseTime += result.Duration

		if i == 0 {
			minDuration = result.Duration
			maxDuration = result.Duration
		} else {
			if result.Duration < minDuration {
				minDuration = result.Duration
			}
			if result.Duration > maxDuration {
				maxDuration = result.Duration
			}
		}
	}

	avgDuration := totalResponseTime / time.Duration(len(results))

	// 输出报告
	fmt.Println("\n" + "=" * 60)
	fmt.Println("测试报告")
	fmt.Println("=" * 60)
	fmt.Printf("总请求数: %d\n", len(results))
	fmt.Printf("成功数: %d (%.2f%%)\n", successCount, float64(successCount)/float64(len(results))*100)
	fmt.Printf("失败数: %d (%.2f%%)\n", failureCount, float64(failureCount)/float64(len(results))*100)
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("平均响应时间: %v\n", avgDuration)
	fmt.Printf("最快响应时间: %v\n", minDuration)
	fmt.Printf("最慢响应时间: %v\n", maxDuration)

	// 记录到日志
	logger.Println("=" * 60)
	logger.Println("TEST REPORT")
	logger.Println("=" * 60)
	logger.Printf("Total Requests: %d", len(results))
	logger.Printf("Success: %d (%.2f%%)", successCount, float64(successCount)/float64(len(results))*100)
	logger.Printf("Failures: %d (%.2f%%)", failureCount, float64(failureCount)/float64(len(results))*100)
	logger.Printf("Total Duration: %v", totalDuration)
	logger.Printf("Average Response Time: %v", avgDuration)
	logger.Printf("Min Response Time: %v", minDuration)
	logger.Printf("Max Response Time: %v", maxDuration)
}

// saveResultsToJSON 保存结果到JSON文件
func saveResultsToJSON(results []TestResult, totalDuration time.Duration) {
	data := map[string]interface{}{
		"test_summary": map[string]interface{}{
			"total_requests":   len(results),
			"total_duration":   totalDuration.String(),
			"start_time":       results[0].Timestamp,
			"end_time":         results[len(results)-1].Timestamp,
		},
		"results": results,
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("Failed to marshal results to JSON: %v", err)
		return
	}

	filename := fmt.Sprintf("api_test_results_%s.json", time.Now().Format("20060102_150405"))
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		log.Printf("Failed to write JSON file: %v", err)
		return
	}

	fmt.Printf("结果已保存到: %s\n", filename)
}
