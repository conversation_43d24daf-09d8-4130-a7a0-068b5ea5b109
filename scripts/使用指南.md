# API 测试脚本使用指南

## 📋 脚本概览

我为您创建了3个测试脚本，满足不同的测试需求：

| 脚本名称 | 功能 | 测试范围 | 推荐场景 |
|---------|------|----------|----------|
| `quick_test.sh` | 快速功能验证 | 5张图片，5秒间隔 | 开发调试、功能验证 |
| `api_test.sh` | 完整压力测试 | 200张图片，15秒间隔 | 生产环境测试、性能评估 |
| `api_test.go` | 高级测试工具 | 200张图片，15秒间隔 | 详细分析、自定义扩展 |

## 🚀 快速开始

### 1. 启动API服务器
```bash
# 在项目根目录下启动服务器
go run cmd/server/main.go
```

### 2. 运行快速测试（推荐先运行）
```bash
# 快速验证API功能是否正常
./scripts/quick_test.sh
```

### 3. 运行完整测试
```bash
# Shell版本（推荐）
./scripts/api_test.sh

# 或 Go版本
go run scripts/api_test.go
```

## 📊 测试结果示例

### 快速测试输出：
```
==========================================
API 快速功能测试
==========================================
[1/5] 测试图片: http://solve.igmdns.com/img/01.jpg
✅ 成功 - 状态码: 200, 响应时间: 11.99s
   题目类型: 单选题

[2/5] 测试图片: http://solve.igmdns.com/img/02.jpg
✅ 成功 - 状态码: 200, 响应时间: 9.89s
   题目类型: 单选题
...
```

### 完整测试报告：
```
========================================
测试报告
========================================
总请求数: 200
成功数: 195 (97.50%)
失败数: 5 (2.50%)
总耗时: 0小时 52分钟 30秒
平均间隔: 15.75秒/请求
```

## 📁 输出文件说明

### Shell版本输出 (`api_test.sh`)：
```
test_results_20241210_141530/
├── api_test.log      # 详细测试日志
├── results.csv       # CSV格式结果（可用Excel打开）
├── errors.log        # 错误详情
└── summary.txt       # 汇总报告
```

### Go版本输出 (`api_test.go`)：
```
api_test_results_20241210_141530.log   # 测试日志
api_test_results_20241210_141530.json  # JSON格式完整结果
```

## ⚙️ 自定义配置

### 修改测试范围：
```bash
# 只测试前50张图片
START_IMAGE=1 END_IMAGE=50 ./scripts/api_test.sh

# 测试特定范围（图片100-150）
START_IMAGE=100 END_IMAGE=150 ./scripts/api_test.sh
```

### 修改请求间隔：
```bash
# 每10秒发送一次请求
INTERVAL=10 ./scripts/api_test.sh

# 每30秒发送一次请求（更保守）
INTERVAL=30 ./scripts/api_test.sh
```

### 修改API地址：
```bash
# 测试不同的服务器
API_URL="http://your-server:8080/api/v1/process-image" ./scripts/api_test.sh
```

## 🔍 结果分析

### 关键指标：
- **成功率**: 应该 > 95%
- **平均响应时间**: 通常 8-15秒
- **最大响应时间**: 应该 < 60秒

### 常见问题：

#### 1. 连接失败
```
❌ 失败 - 请求失败或超时
```
**解决方案**：
- 检查API服务器是否运行
- 检查端口8080是否被占用
- 增加超时时间：`TIMEOUT=120 ./scripts/api_test.sh`

#### 2. 500内部服务器错误
```
❌ 失败 - 状态码: 500
```
**解决方案**：
- 查看服务器日志
- 检查数据库连接
- 检查AI服务配置（API密钥等）

#### 3. 响应时间过长
```
✅ 成功 - 状态码: 200, 响应时间: 45.32s
```
**可能原因**：
- AI服务响应慢
- 网络延迟
- 服务器负载高

## 📈 性能基准

### 预期性能指标：
- **成功率**: 95-99%
- **平均响应时间**: 8-15秒
- **P95响应时间**: < 25秒
- **P99响应时间**: < 40秒

### 完整测试时间估算：
- **200张图片 × 15秒间隔 = 50分钟**
- **加上API处理时间 ≈ 55-60分钟**

## 🛠️ 故障排除

### 1. 脚本权限问题：
```bash
chmod +x scripts/*.sh
```

### 2. 依赖检查：
```bash
# 检查curl是否安装
curl --version

# 检查bc计算器（Shell脚本需要）
bc --version

# 检查jq（可选，用于JSON解析）
jq --version
```

### 3. 服务器状态检查：
```bash
# 检查健康状态
curl http://localhost:8080/api/v1/health

# 检查端口占用
lsof -i :8080
```

## 📝 测试建议

### 开发阶段：
1. 先运行 `quick_test.sh` 验证基本功能
2. 修复发现的问题
3. 运行小范围测试（如前20张图片）
4. 最后运行完整测试

### 生产环境：
1. 在低峰期运行测试
2. 监控服务器资源使用情况
3. 保存测试结果用于性能对比
4. 定期运行回归测试

## 🔧 扩展功能

### 添加并发测试：
可以修改Go版本脚本，添加并发请求功能来测试服务器的并发处理能力。

### 添加性能监控：
可以结合系统监控工具（如htop、iostat）来观察测试期间的系统资源使用情况。

### 自动化集成：
可以将测试脚本集成到CI/CD流水线中，实现自动化测试。

---

**注意**: 长时间的压力测试可能会对服务器造成负载，建议在测试环境中进行，或在生产环境的低峰期谨慎执行。
