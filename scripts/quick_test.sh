#!/bin/bash

# 快速测试脚本 - 用于快速验证API功能
# 只测试前5张图片，间隔5秒

echo "=========================================="
echo "API 快速功能测试"
echo "=========================================="
echo "测试范围: 图片 01-05"
echo "请求间隔: 5秒"
echo "API地址: http://localhost:8080/api/v1/process-image"
echo "=========================================="

# 检查服务器是否运行
echo "检查API服务器状态..."
if curl -s http://localhost:8080/api/v1/health > /dev/null; then
    echo "✅ API服务器运行正常"
else
    echo "❌ API服务器未运行，请先启动服务器："
    echo "   go run cmd/server/main.go"
    exit 1
fi

echo ""
echo "开始快速测试..."
echo ""

# 测试前5张图片
for i in {1..5}; do
    IMAGE_NUM=$(printf "%02d" $i)
    IMAGE_URL="http://solve.igmdns.com/img/${IMAGE_NUM}.jpg"
    
    echo "[$i/5] 测试图片: $IMAGE_URL"
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    
    # 发送请求
    RESPONSE=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "{\"image_url\": \"$IMAGE_URL\"}" \
        --max-time 30 \
        http://localhost:8080/api/v1/process-image)
    
    # 计算响应时间
    END_TIME=$(date +%s.%N)
    DURATION=$(echo "$END_TIME - $START_TIME" | bc)
    
    # 提取状态码
    STATUS_CODE="${RESPONSE: -3}"
    RESPONSE_BODY="${RESPONSE%???}"
    
    # 显示结果
    if [ "$STATUS_CODE" = "200" ]; then
        echo "✅ 成功 - 状态码: $STATUS_CODE, 响应时间: ${DURATION}s"
        # 尝试解析响应中的题目类型
        QUESTION_TYPE=$(echo "$RESPONSE_BODY" | grep -o '"question_type":"[^"]*"' | cut -d'"' -f4)
        if [ ! -z "$QUESTION_TYPE" ]; then
            echo "   题目类型: $QUESTION_TYPE"
        fi
    else
        echo "❌ 失败 - 状态码: $STATUS_CODE, 响应时间: ${DURATION}s"
        if [ ${#RESPONSE_BODY} -lt 200 ]; then
            echo "   错误信息: $RESPONSE_BODY"
        fi
    fi
    
    # 等待间隔（除了最后一次）
    if [ $i -lt 5 ]; then
        echo "   等待 5 秒..."
        sleep 5
    fi
    
    echo ""
done

echo "=========================================="
echo "快速测试完成！"
echo "=========================================="
echo ""
echo "如需完整测试，请运行："
echo "  ./scripts/api_test.sh    # 完整的200张图片测试"
echo "  go run scripts/api_test.go  # Go版本测试"
