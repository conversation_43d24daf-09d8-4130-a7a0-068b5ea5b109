#!/bin/bash

# API测试脚本
# 功能：轮询测试API，从图片01到200，每次间隔15秒

# 配置参数
API_URL="http://localhost:8080/api/v1/process-image"
IMAGE_BASE_URL="http://solve.igmdns.com/img/"
START_IMAGE=1
END_IMAGE=200
INTERVAL=15
TIMEOUT=60

# 创建结果目录
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULT_DIR="test_results_${TIMESTAMP}"
mkdir -p "$RESULT_DIR"

# 日志文件
LOG_FILE="${RESULT_DIR}/api_test.log"
RESULT_FILE="${RESULT_DIR}/results.csv"
ERROR_FILE="${RESULT_DIR}/errors.log"

# 初始化CSV文件
echo "序号,图片编号,图片URL,状态码,响应时间(秒),是否成功,错误信息,请求时间" > "$RESULT_FILE"

# 计数器
SUCCESS_COUNT=0
FAILURE_COUNT=0
TOTAL_COUNT=0

# 开始时间
START_TIME=$(date +%s)

echo "========================================" | tee -a "$LOG_FILE"
echo "API 测试开始" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"
echo "API地址: $API_URL" | tee -a "$LOG_FILE"
echo "图片范围: ${START_IMAGE} - ${END_IMAGE}" | tee -a "$LOG_FILE"
echo "请求间隔: ${INTERVAL}秒" | tee -a "$LOG_FILE"
echo "超时时间: ${TIMEOUT}秒" | tee -a "$LOG_FILE"
echo "结果目录: $RESULT_DIR" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"

# 循环测试
for i in $(seq -f "%02g" $START_IMAGE $END_IMAGE); do
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    IMAGE_URL="${IMAGE_BASE_URL}${i}.jpg"
    REQUEST_TIME=$(date "+%Y-%m-%d %H:%M:%S")
    
    echo "" | tee -a "$LOG_FILE"
    echo "[$TOTAL_COUNT/$END_IMAGE] 测试图片: $IMAGE_URL" | tee -a "$LOG_FILE"
    echo "请求时间: $REQUEST_TIME" | tee -a "$LOG_FILE"
    
    # 记录请求开始时间
    REQUEST_START=$(date +%s.%N)
    
    # 发送API请求
    RESPONSE=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "{\"image_url\": \"$IMAGE_URL\"}" \
        --max-time $TIMEOUT \
        "$API_URL" 2>&1)
    
    # 解析响应
    if [ $? -eq 0 ]; then
        # 提取状态码和响应时间
        STATUS_CODE=$(echo "$RESPONSE" | tail -n 2 | head -n 1)
        RESPONSE_TIME=$(echo "$RESPONSE" | tail -n 1)
        RESPONSE_BODY=$(echo "$RESPONSE" | head -n -2)
        
        # 检查状态码
        if [ "$STATUS_CODE" = "200" ]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "✅ 成功 - 状态码: $STATUS_CODE, 响应时间: ${RESPONSE_TIME}s" | tee -a "$LOG_FILE"
            echo "$TOTAL_COUNT,$i,$IMAGE_URL,$STATUS_CODE,$RESPONSE_TIME,成功,,$REQUEST_TIME" >> "$RESULT_FILE"
        else
            FAILURE_COUNT=$((FAILURE_COUNT + 1))
            ERROR_MSG="HTTP $STATUS_CODE"
            echo "❌ 失败 - 状态码: $STATUS_CODE, 响应时间: ${RESPONSE_TIME}s" | tee -a "$LOG_FILE"
            echo "响应内容: $RESPONSE_BODY" | tee -a "$ERROR_FILE"
            echo "$TOTAL_COUNT,$i,$IMAGE_URL,$STATUS_CODE,$RESPONSE_TIME,失败,$ERROR_MSG,$REQUEST_TIME" >> "$RESULT_FILE"
        fi
    else
        FAILURE_COUNT=$((FAILURE_COUNT + 1))
        ERROR_MSG="请求失败或超时"
        echo "❌ 失败 - $ERROR_MSG" | tee -a "$LOG_FILE"
        echo "错误详情: $RESPONSE" | tee -a "$ERROR_FILE"
        echo "$TOTAL_COUNT,$i,$IMAGE_URL,0,0,失败,$ERROR_MSG,$REQUEST_TIME" >> "$RESULT_FILE"
    fi
    
    # 显示进度
    PROGRESS=$((TOTAL_COUNT * 100 / END_IMAGE))
    echo "进度: $PROGRESS% ($SUCCESS_COUNT 成功, $FAILURE_COUNT 失败)" | tee -a "$LOG_FILE"
    
    # 如果不是最后一次请求，等待间隔时间
    if [ $TOTAL_COUNT -lt $END_IMAGE ]; then
        echo "等待 ${INTERVAL} 秒后继续..." | tee -a "$LOG_FILE"
        sleep $INTERVAL
    fi
    
    echo "----------------------------------------" | tee -a "$LOG_FILE"
done

# 计算总耗时
END_TIME=$(date +%s)
TOTAL_DURATION=$((END_TIME - START_TIME))
HOURS=$((TOTAL_DURATION / 3600))
MINUTES=$(((TOTAL_DURATION % 3600) / 60))
SECONDS=$((TOTAL_DURATION % 60))

# 生成测试报告
echo "" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"
echo "测试报告" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"
echo "总请求数: $TOTAL_COUNT" | tee -a "$LOG_FILE"
echo "成功数: $SUCCESS_COUNT ($(echo "scale=2; $SUCCESS_COUNT * 100 / $TOTAL_COUNT" | bc)%)" | tee -a "$LOG_FILE"
echo "失败数: $FAILURE_COUNT ($(echo "scale=2; $FAILURE_COUNT * 100 / $TOTAL_COUNT" | bc)%)" | tee -a "$LOG_FILE"
echo "总耗时: ${HOURS}小时 ${MINUTES}分钟 ${SECONDS}秒" | tee -a "$LOG_FILE"
echo "平均间隔: $(echo "scale=2; $TOTAL_DURATION / $TOTAL_COUNT" | bc)秒/请求" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"

# 创建汇总报告文件
SUMMARY_FILE="${RESULT_DIR}/summary.txt"
cat > "$SUMMARY_FILE" << EOF
API测试汇总报告
================

测试配置:
- API地址: $API_URL
- 图片范围: ${START_IMAGE} - ${END_IMAGE}
- 请求间隔: ${INTERVAL}秒
- 超时时间: ${TIMEOUT}秒

测试结果:
- 总请求数: $TOTAL_COUNT
- 成功数: $SUCCESS_COUNT ($(echo "scale=2; $SUCCESS_COUNT * 100 / $TOTAL_COUNT" | bc)%)
- 失败数: $FAILURE_COUNT ($(echo "scale=2; $FAILURE_COUNT * 100 / $TOTAL_COUNT" | bc)%)
- 总耗时: ${HOURS}小时 ${MINUTES}分钟 ${SECONDS}秒

文件说明:
- api_test.log: 详细测试日志
- results.csv: 测试结果CSV文件
- errors.log: 错误详情日志
- summary.txt: 本汇总报告

测试完成时间: $(date "+%Y-%m-%d %H:%M:%S")
EOF

echo ""
echo "测试完成！"
echo "结果文件保存在: $RESULT_DIR"
echo "- 详细日志: $LOG_FILE"
echo "- 结果CSV: $RESULT_FILE"
echo "- 错误日志: $ERROR_FILE"
echo "- 汇总报告: $SUMMARY_FILE"

# 如果有失败的请求，显示错误统计
if [ $FAILURE_COUNT -gt 0 ]; then
    echo ""
    echo "失败请求统计:"
    echo "============="
    grep "失败" "$RESULT_FILE" | cut -d',' -f2,7 | sort | uniq -c | while read count image_num error; do
        echo "图片 $image_num: $count 次失败 ($error)"
    done
fi
