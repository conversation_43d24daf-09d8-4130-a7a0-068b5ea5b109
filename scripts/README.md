# API 测试脚本使用说明

本目录包含两个API测试脚本，用于对图片处理API进行压力测试和功能验证。

## 脚本说明

### 1. Go版本测试脚本 (`api_test.go`)

**功能特点：**
- 使用Go语言编写，性能更好
- 支持并发请求（可扩展）
- 详细的JSON格式结果输出
- 完整的错误处理和统计

**使用方法：**
```bash
# 运行Go测试脚本
go run scripts/api_test.go
```

**输出文件：**
- `api_test_results_YYYYMMDD_HHMMSS.log` - 详细测试日志
- `api_test_results_YYYYMMDD_HHMMSS.json` - JSON格式的完整测试结果

### 2. Bash版本测试脚本 (`api_test.sh`)

**功能特点：**
- 使用Shell脚本编写，无需额外依赖
- 实时显示测试进度
- CSV格式结果输出，便于Excel分析
- 自动生成汇总报告

**使用方法：**
```bash
# 运行Shell测试脚本
./scripts/api_test.sh
```

**输出文件：**
- `test_results_YYYYMMDD_HHMMSS/api_test.log` - 详细测试日志
- `test_results_YYYYMMDD_HHMMSS/results.csv` - CSV格式测试结果
- `test_results_YYYYMMDD_HHMMSS/errors.log` - 错误详情日志
- `test_results_YYYYMMDD_HHMMSS/summary.txt` - 汇总报告

## 测试配置

### 默认配置参数：
- **API地址**: `http://localhost:8080/api/v1/process-image`
- **图片范围**: 01.jpg - 200.jpg
- **图片基础URL**: `http://solve.igmdns.com/img/`
- **请求间隔**: 15秒
- **超时时间**: 60秒

### 修改配置：

**Go版本** - 修改 `api_test.go` 中的 `TestConfig` 结构：
```go
config := TestConfig{
    BaseURL:      "http://localhost:8080/api/v1/process-image",
    ImageBaseURL: "http://solve.igmdns.com/img/",
    StartImage:   1,
    EndImage:     200,
    Interval:     15 * time.Second,
    Timeout:      60 * time.Second,
}
```

**Bash版本** - 修改 `api_test.sh` 开头的配置变量：
```bash
API_URL="http://localhost:8080/api/v1/process-image"
IMAGE_BASE_URL="http://solve.igmdns.com/img/"
START_IMAGE=1
END_IMAGE=200
INTERVAL=15
TIMEOUT=60
```

## 测试流程

1. **启动API服务器**
   ```bash
   go run cmd/server/main.go
   ```

2. **运行测试脚本**
   ```bash
   # 选择其中一个脚本运行
   go run scripts/api_test.go
   # 或
   ./scripts/api_test.sh
   ```

3. **查看测试结果**
   - 实时查看控制台输出
   - 测试完成后查看生成的结果文件

## 测试结果分析

### 成功指标：
- HTTP状态码为200
- 响应时间在合理范围内
- 返回正确的JSON格式数据

### 关键指标：
- **成功率**: 成功请求数 / 总请求数
- **平均响应时间**: 所有请求的平均处理时间
- **最大响应时间**: 最慢的请求处理时间
- **错误分布**: 不同类型错误的统计

### 常见问题排查：

1. **连接超时**
   - 检查API服务器是否正常运行
   - 检查网络连接
   - 适当增加超时时间

2. **500内部服务器错误**
   - 查看服务器日志
   - 检查数据库连接
   - 检查AI服务配置

3. **404错误**
   - 检查API地址是否正确
   - 确认服务器路由配置

## 预期测试时间

- **总请求数**: 200次
- **请求间隔**: 15秒
- **预计总时间**: 约50分钟 (200 × 15秒 ÷ 60)
- **实际时间**: 会因API响应时间而略有增加

## 注意事项

1. **服务器负载**: 长时间测试可能对服务器造成负载，建议在测试环境进行
2. **网络稳定性**: 确保网络连接稳定，避免因网络问题导致的误报
3. **磁盘空间**: 测试会生成日志文件，确保有足够的磁盘空间
4. **API限制**: 如果API有频率限制，可能需要调整请求间隔

## 扩展功能

### 自定义测试范围：
```bash
# 只测试前10张图片
START_IMAGE=1 END_IMAGE=10 ./scripts/api_test.sh
```

### 修改请求间隔：
```bash
# 每5秒发送一次请求
INTERVAL=5 ./scripts/api_test.sh
```

### 测试特定图片：
```bash
# 只测试图片25
START_IMAGE=25 END_IMAGE=25 ./scripts/api_test.sh
```
