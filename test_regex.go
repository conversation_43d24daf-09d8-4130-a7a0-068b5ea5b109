package main

import (
	"fmt"
	"regexp"
	"strings"
)

// CleanQuestionText 当前的清洗函数
func CleanQuestionText(questionText string) string {
	// 当前使用的正则表达式
	regex := regexp.MustCompile(`^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`)
	
	// 清除匹配的前缀
	cleaned := regex.ReplaceAllString(questionText, "")
	
	// 去除首尾空白字符
	return strings.TrimSpace(cleaned)
}

// ImprovedCleanQuestionText 改进的清洗函数
func ImprovedCleanQuestionText(questionText string) string {
	// 改进的正则表达式，更全面地匹配各种情况
	patterns := []string{
		// 完整格式：(题目类型)序号、
		`^[(（【]?(判断题|单选题|多选题|断题|题)[)）】]?\s*0?(?:[1-9]|[1-9][0-9]|1[0-9][0-9]|200)[、.．]\s*`,
		// 不完整格式：断题)序号、 或 题)序号、
		`^(判断题|单选题|多选题|断题|题)[)）】]?\s*0?(?:[1-9]|[1-9][0-9]|1[0-9][0-9]|200)[、.．]\s*`,
		// 只有序号：序号、 或 序号.
		`^0?(?:[1-9]|[1-9][0-9]|1[0-9][0-9]|200)[、.．]\s*`,
		// 不完整的括号：(题目类型序号、
		`^[(（【]?(判断题|单选题|多选题|断题|题)\s*0?(?:[1-9]|[1-9][0-9]|1[0-9][0-9]|200)[、.．]\s*`,
	}
	
	cleaned := questionText
	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		cleaned = regex.ReplaceAllString(cleaned, "")
		// 如果这个模式匹配了，就不需要继续尝试其他模式
		if cleaned != questionText {
			break
		}
	}
	
	// 去除首尾空白字符
	return strings.TrimSpace(cleaned)
}

func main() {
	// 测试用例
	testCases := []string{
		"(判断题)08、等17分钟后如果没问题就可以走了。",
		"(判断题)08.等17分钟后如果没问题就可以走了。",
		"08.等17分钟后如果没问题就可以走了。",
		"08、等17分钟后如果没问题就可以走了。",
		"断题)08、等17分钟后如果没问题就可以走了。",
		"判断题08、等17分钟后如果没问题就可以走了。",
		"题)08、等17分钟后如果没问题就可以走了。",
		"(单选题)01、驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
		"01、驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
		"(多选题)15.雾天跟车行驶,应如何安全驾驶?",
		"15.雾天跟车行驶,应如何安全驾驶?",
		"雾天跟车行驶,应如何安全驾驶?", // 正常情况，不应该被清洗
	}

	fmt.Println("正则表达式测试结果:")
	fmt.Println(strings.Repeat("=", 80))
	
	for i, testCase := range testCases {
		fmt.Printf("测试用例 %d:\n", i+1)
		fmt.Printf("原文: %s\n", testCase)
		
		currentResult := CleanQuestionText(testCase)
		improvedResult := ImprovedCleanQuestionText(testCase)
		
		fmt.Printf("当前结果: %s\n", currentResult)
		fmt.Printf("改进结果: %s\n", improvedResult)
		
		// 检查是否有改进
		if currentResult != improvedResult {
			fmt.Printf("🔧 改进效果: 有差异\n")
		} else {
			fmt.Printf("✅ 结果一致\n")
		}
		
		fmt.Println(strings.Repeat("-", 40))
	}
	
	// 测试当前正则的具体问题
	fmt.Println("\n当前正则表达式分析:")
	fmt.Println(strings.Repeat("=", 50))
	
	currentRegex := `^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`
	fmt.Printf("当前正则: %s\n", currentRegex)
	
	problemCases := []string{
		"断题)08、等17分钟后如果没问题就可以走了。",
		"判断题08、等17分钟后如果没问题就可以走了。",
		"题)08、等17分钟后如果没问题就可以走了。",
	}
	
	regex := regexp.MustCompile(currentRegex)
	
	for _, testCase := range problemCases {
		matches := regex.FindAllString(testCase, -1)
		fmt.Printf("测试: %s\n", testCase)
		if len(matches) > 0 {
			fmt.Printf("匹配到: %v\n", matches)
		} else {
			fmt.Printf("❌ 未匹配\n")
		}
		fmt.Println()
	}
}
