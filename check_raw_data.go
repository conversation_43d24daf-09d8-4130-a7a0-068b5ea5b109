package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库连接配置
	dsn := "gmdns:5e7fFn3HpPfuQ6Qx42Az@tcp(47.96.0.212:3380)/solve_api_go?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("成功连接到数据库")

	// 查询一条记录查看qwen_raw的原始结构
	query := `
		SELECT id, question_text, qwen_raw 
		FROM questions 
		WHERE qwen_raw IS NOT NULL AND qwen_raw != ''
		ORDER BY id DESC
		LIMIT 1
	`

	var id int64
	var questionText string
	var qwenRawBytes []byte

	err = db.QueryRow(query).Scan(&id, &questionText, &qwenRawBytes)
	if err != nil {
		log.Fatalf("Failed to query: %v", err)
	}

	fmt.Printf("记录ID: %d\n", id)
	fmt.Printf("Question Text: %s\n", questionText)
	fmt.Printf("Qwen Raw 长度: %d bytes\n", len(qwenRawBytes))
	
	// 显示原始JSON结构
	fmt.Println("\nQwen Raw 原始数据:")
	fmt.Println(strings.Repeat("=", 50))
	
	// 格式化显示JSON
	var rawData interface{}
	if err := json.Unmarshal(qwenRawBytes, &rawData); err != nil {
		fmt.Printf("JSON解析失败: %v\n", err)
		fmt.Printf("原始字节数据: %s\n", string(qwenRawBytes))
	} else {
		prettyJSON, err := json.MarshalIndent(rawData, "", "  ")
		if err != nil {
			fmt.Printf("格式化JSON失败: %v\n", err)
		} else {
			fmt.Printf("%s\n", string(prettyJSON))
		}
	}
}
