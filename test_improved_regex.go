package main

import (
	"fmt"
	"regexp"
	"strings"
)

// ImprovedCleanQuestionText 改进的清洗函数
func ImprovedCleanQuestionText(questionText string) string {
	// 改进的正则表达式，修复两位数序号匹配问题
	// 按优先级顺序匹配，避免部分匹配问题
	patterns := []string{
		// 1. 完整格式：(题目类型)序号、内容
		`^[(（【]?(判断题|单选题|多选题|断题|题)[)）】]?\s*0?(?:[1-9][0-9]{0,2}|[1-9])[、.．]\s*`,
		// 2. 不完整格式：断题)序号、内容 或 题)序号、内容
		`^(判断题|单选题|多选题|断题|题)[)）】]?\s*0?(?:[1-9][0-9]{0,2}|[1-9])[、.．]\s*`,
		// 3. 题目类型无括号：判断题序号、内容
		`^(判断题|单选题|多选题|断题|题)\s*0?(?:[1-9][0-9]{0,2}|[1-9])[、.．]\s*`,
		// 4. 只有序号：序号、内容 或 序号.内容
		`^0?(?:[1-9][0-9]{0,2}|[1-9])[、.．]\s*`,
		// 5. 不完整的括号：(题目类型序号、内容（缺少右括号）
		`^[(（【]?(判断题|单选题|多选题|断题|题)\s*0?(?:[1-9][0-9]{0,2}|[1-9])[、.．]\s*`,
	}
	
	cleaned := questionText
	
	// 按优先级尝试每个模式，找到第一个匹配的就停止
	for i, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		newCleaned := regex.ReplaceAllString(cleaned, "")
		
		// 如果这个模式匹配了（即文本发生了变化），就使用这个结果
		if newCleaned != cleaned {
			fmt.Printf("  匹配模式 %d: %s\n", i+1, pattern)
			cleaned = newCleaned
			break
		}
	}
	
	// 去除首尾空白字符
	return strings.TrimSpace(cleaned)
}

func main() {
	// 扩展的测试用例，包括更多边界情况
	testCases := []struct {
		input    string
		expected string
		desc     string
	}{
		{
			"(判断题)08、等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"标准格式：(题目类型)序号、",
		},
		{
			"(判断题)08.等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"标准格式：(题目类型)序号.",
		},
		{
			"08.等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"只有序号：序号.",
		},
		{
			"08、等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"只有序号：序号、",
		},
		{
			"断题)08、等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"不完整格式：断题)序号、",
		},
		{
			"判断题08、等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"无括号格式：判断题序号、",
		},
		{
			"题)08、等17分钟后如果没问题就可以走了。",
			"等17分钟后如果没问题就可以走了。",
			"简化格式：题)序号、",
		},
		{
			"(单选题)01、驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
			"驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
			"单位数序号",
		},
		{
			"(多选题)15.雾天跟车行驶,应如何安全驾驶?",
			"雾天跟车行驶,应如何安全驾驶?",
			"两位数序号（关键测试）",
		},
		{
			"15.雾天跟车行驶,应如何安全驾驶?",
			"雾天跟车行驶,应如何安全驾驶?",
			"两位数序号，无题目类型",
		},
		{
			"(判断题)125、这是一个三位数序号的题目。",
			"这是一个三位数序号的题目。",
			"三位数序号",
		},
		{
			"雾天跟车行驶,应如何安全驾驶?",
			"雾天跟车行驶,应如何安全驾驶?",
			"正常题目，不应该被清洗",
		},
		{
			"(多选题)200.这是最大序号的题目。",
			"这是最大序号的题目。",
			"最大序号测试",
		},
		{
			"（单选题）99、中文括号测试。",
			"中文括号测试。",
			"中文括号格式",
		},
		{
			"【判断题】88.方括号测试。",
			"方括号测试。",
			"方括号格式",
		},
	}

	fmt.Println("改进的正则表达式测试结果:")
	fmt.Println(strings.Repeat("=", 80))
	
	successCount := 0
	totalCount := len(testCases)
	
	for i, testCase := range testCases {
		fmt.Printf("\n测试用例 %d: %s\n", i+1, testCase.desc)
		fmt.Printf("原文: %s\n", testCase.input)
		fmt.Printf("期望: %s\n", testCase.expected)
		
		result := ImprovedCleanQuestionText(testCase.input)
		fmt.Printf("结果: %s\n", result)
		
		if result == testCase.expected {
			fmt.Printf("✅ 通过\n")
			successCount++
		} else {
			fmt.Printf("❌ 失败\n")
		}
		
		fmt.Println(strings.Repeat("-", 40))
	}
	
	fmt.Printf("\n测试总结:\n")
	fmt.Printf("总测试数: %d\n", totalCount)
	fmt.Printf("通过数: %d\n", successCount)
	fmt.Printf("失败数: %d\n", totalCount-successCount)
	fmt.Printf("成功率: %.2f%%\n", float64(successCount)/float64(totalCount)*100)
	
	if successCount == totalCount {
		fmt.Println("🎉 所有测试通过！")
	} else {
		fmt.Println("⚠️  有测试失败，需要进一步优化")
	}
}
