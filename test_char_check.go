package main

import (
	"fmt"
)

func main() {
	questionType := "判断题"
	text := "断题)08、等17分钟后如果没问题就可以走了。"
	
	fmt.Printf("questionType length: %d\n", len(questionType))
	fmt.Printf("questionType runes: %d\n", len([]rune(questionType)))
	
	// 使用rune来处理中文字符
	runes := []rune(questionType)
	if len(runes) > 1 {
		incompleteType := string(runes[1:]) // "断题"
		fmt.Printf("incompleteType: %s\n", incompleteType)
		
		pattern := incompleteType + ")"
		fmt.Printf("pattern: %s\n", pattern)
		
		// 检查text是否以pattern开头
		textRunes := []rune(text)
		patternRunes := []rune(pattern)
		
		fmt.Printf("text start: %s\n", string(textRunes[:len(patternRunes)]))
		
		if string(textRunes[:len(patternRunes)]) == pattern {
			fmt.Println("✅ 匹配成功!")
		} else {
			fmt.Println("❌ 不匹配")
		}
	}
}
