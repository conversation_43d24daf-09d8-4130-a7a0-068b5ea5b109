package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

// QwenData Qwen返回的数据结构
type QwenData struct {
	QuestionType string `json:"question_type"`
	QuestionText string `json:"question_text"`
	QuestionNum  string `json:"question_num"`
	A            string `json:"A,omitempty"`
	B            string `json:"B,omitempty"`
	C            string `json:"C,omitempty"`
	D            string `json:"D,omitempty"`
	Y            string `json:"Y,omitempty"`
	N            string `json:"N,omitempty"`
}

// 当前的清洗函数
func currentCleanQuestionText(questionText string) string {
	regex := regexp.MustCompile(`^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`)
	cleaned := regex.ReplaceAllString(questionText, "")
	return strings.TrimSpace(cleaned)
}

func main() {
	// 数据库连接
	dsn := "gmdns:5e7fFn3HpPfuQ6Qx42Az@tcp(47.96.0.212:3380)/solve_api_go?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	// 查询最近的一些记录
	query := `
		SELECT id, question_text, qwen_raw
		FROM questions
		WHERE qwen_raw IS NOT NULL
		ORDER BY created_at DESC
		LIMIT 10
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Fatal("查询失败:", err)
	}
	defer rows.Close()

	fmt.Println("数据库中question_text与qwen_raw对比分析:")
	fmt.Println(strings.Repeat("=", 80))

	count := 0
	problemCount := 0

	for rows.Next() {
		var id int
		var questionText string
		var qwenRawBytes []byte

		err := rows.Scan(&id, &questionText, &qwenRawBytes)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		count++
		fmt.Printf("\n记录 %d (ID: %d):\n", count, id)
		fmt.Printf("数据库中的question_text: %s\n", questionText)

		// 解析qwen_raw
		var qwenData QwenData
		if err := json.Unmarshal(qwenRawBytes, &qwenData); err != nil {
			// 尝试从嵌套结构中解析
			var nestedData map[string]interface{}
			if err2 := json.Unmarshal(qwenRawBytes, &nestedData); err2 == nil {
				if output, ok := nestedData["output"].(map[string]interface{}); ok {
					if choices, ok := output["choices"].([]interface{}); ok && len(choices) > 0 {
						if choice, ok := choices[0].(map[string]interface{}); ok {
							if message, ok := choice["message"].(map[string]interface{}); ok {
								if content, ok := message["content"].([]interface{}); ok && len(content) > 0 {
									if contentItem, ok := content[0].(map[string]interface{}); ok {
										if text, ok := contentItem["text"].(string); ok {
											// 解析text中的JSON
											if err3 := json.Unmarshal([]byte(text), &qwenData); err3 != nil {
												log.Printf("解析text中的JSON失败: %v", err3)
												continue
											}
										}
									}
								}
							}
						}
					}
				}
			}
			if qwenData.QuestionText == "" {
				log.Printf("解析qwen_raw失败: %v", err)
				continue
			}
		}

		fmt.Printf("Qwen原始question_text: %s\n", qwenData.QuestionText)

		// 测试当前清洗函数
		cleaned := currentCleanQuestionText(qwenData.QuestionText)
		fmt.Printf("当前清洗后结果: %s\n", cleaned)

		// 检查是否有问题
		if cleaned != questionText {
			problemCount++
			fmt.Printf("🔍 发现差异！\n")
			fmt.Printf("  期望结果: %s\n", questionText)
			fmt.Printf("  实际结果: %s\n", cleaned)
			
			// 分析原始文本的特征
			fmt.Printf("  原始文本分析:\n")
			if strings.Contains(qwenData.QuestionText, "(") || strings.Contains(qwenData.QuestionText, "（") {
				fmt.Printf("    - 包含括号\n")
			}
			if regexp.MustCompile(`^\d+[、.]`).MatchString(qwenData.QuestionText) {
				fmt.Printf("    - 以数字序号开头\n")
			}
			if regexp.MustCompile(`^[(（【]?(判断题|单选题|多选题)`).MatchString(qwenData.QuestionText) {
				fmt.Printf("    - 包含题目类型\n")
			}
		} else {
			fmt.Printf("✅ 清洗正确\n")
		}

		fmt.Println(strings.Repeat("-", 60))
	}

	fmt.Printf("\n总结:\n")
	fmt.Printf("总记录数: %d\n", count)
	fmt.Printf("有问题的记录数: %d\n", problemCount)
	if count > 0 {
		fmt.Printf("问题比例: %.2f%%\n", float64(problemCount)/float64(count)*100)
	}

	if err := rows.Err(); err != nil {
		log.Fatal("遍历行时出错:", err)
	}
}
