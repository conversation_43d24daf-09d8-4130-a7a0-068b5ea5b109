package main

import (
	"fmt"
	"strings"
)

func main() {
	// 测试一个简单的案例
	questionType := "判断题"
	questionNum := "8"
	text := "断题)08、等17分钟后如果没问题就可以走了。"
	
	fmt.Printf("原文: %s\n", text)
	fmt.Printf("题目类型: %s\n", questionType)
	fmt.Printf("序号: %s\n", questionNum)
	
	// 测试不完整类型匹配
	if len(questionType) > 1 {
		incompleteType := questionType[1:] // "断题"
		pattern := incompleteType + ")"    // "断题)"
		
		fmt.Printf("不完整类型: %s\n", incompleteType)
		fmt.Printf("测试模式: %s\n", pattern)
		
		if strings.HasPrefix(text, pattern) {
			fmt.Printf("✅ 类型模式匹配成功!\n")
			step1 := strings.TrimPrefix(text, pattern)
			fmt.Printf("步骤1结果: %s\n", step1)
			
			// 测试序号清洗
			numberPattern := "08、"
			if strings.HasPrefix(step1, numberPattern) {
				fmt.Printf("✅ 序号模式匹配成功!\n")
				step2 := strings.TrimPrefix(step1, numberPattern)
				fmt.Printf("步骤2结果: %s\n", step2)
			} else {
				fmt.Printf("❌ 序号模式不匹配\n")
			}
		} else {
			fmt.Printf("❌ 类型模式不匹配\n")
		}
	}
}
