package main

import (
	"fmt"
	"regexp"
	"strings"
)

func main() {
	// 当前的正则表达式
	currentRegex := `^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`
	
	// 问题测试用例
	testCases := []string{
		"(多选题)15.雾天跟车行驶,应如何安全驾驶?",
		"15.雾天跟车行驶,应如何安全驾驶?",
		"(单选题)10、这是10号题目",
		"10、这是10号题目",
		"(判断题)20.这是20号题目",
		"20.这是20号题目",
	}
	
	fmt.Println("当前正则表达式问题分析:")
	fmt.Println("正则: " + currentRegex)
	fmt.Println(strings.Repeat("=", 60))
	
	regex := regexp.MustCompile(currentRegex)
	
	for _, testCase := range testCases {
		fmt.Printf("\n测试: %s\n", testCase)
		
		// 查找匹配
		matches := regex.FindAllString(testCase, -1)
		if len(matches) > 0 {
			fmt.Printf("匹配到: %s\n", matches[0])
			
			// 执行替换
			result := regex.ReplaceAllString(testCase, "")
			fmt.Printf("替换后: %s\n", result)
		} else {
			fmt.Printf("❌ 未匹配到任何内容\n")
		}
	}
	
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("问题分析:")
	fmt.Println("当前正则 (?:[1-9]|1[0-9]|20) 的问题:")
	fmt.Println("- 对于 '15'，会优先匹配第一个选择 [1-9]，匹配到 '1'")
	fmt.Println("- 剩下的 '5' 不会被匹配，导致清洗不完整")
	fmt.Println("- 需要调整匹配顺序，让长的模式优先匹配")
}
