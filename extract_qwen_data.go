package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

// QwenData Qwen返回的数据结构
type QwenData struct {
	QuestionType string `json:"question_type"`
	QuestionText string `json:"question_text"`
	QuestionNum  string `json:"question_num"`
	A            string `json:"A,omitempty"`
	B            string `json:"B,omitempty"`
	C            string `json:"C,omitempty"`
	D            string `json:"D,omitempty"`
	Y            string `json:"Y,omitempty"`
	N            string `json:"N,omitempty"`
}

// TestCase 测试用例结构
type TestCase struct {
	ID           int64    `json:"id"`
	QuestionText string   `json:"question_text"`
	QwenData     QwenData `json:"qwen_data"`
}

func main() {
	// 数据库连接配置
	dsn := "gmdns:5e7fFn3HpPfuQ6Qx42Az@tcp(47.96.0.212:3380)/solve_api_go?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("成功连接到数据库")

	// 查询所有包含qwen_raw数据的记录
	query := `
		SELECT id, question_text, qwen_raw 
		FROM questions 
		WHERE qwen_raw IS NOT NULL AND qwen_raw != ''
		ORDER BY id
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Fatalf("Failed to query database: %v", err)
	}
	defer rows.Close()

	var testCases []TestCase
	var totalCount int

	for rows.Next() {
		var id int64
		var questionText string
		var qwenRawBytes []byte

		if err := rows.Scan(&id, &questionText, &qwenRawBytes); err != nil {
			log.Printf("Failed to scan row: %v", err)
			continue
		}

		totalCount++

		// 解析qwen_raw JSON数据
		var qwenData QwenData
		
		// 首先尝试直接解析
		if err := json.Unmarshal(qwenRawBytes, &qwenData); err != nil {
			// 如果失败，尝试从嵌套结构中解析
			var nestedData map[string]interface{}
			if err2 := json.Unmarshal(qwenRawBytes, &nestedData); err2 == nil {
				if output, ok := nestedData["output"].(map[string]interface{}); ok {
					if choices, ok := output["choices"].([]interface{}); ok && len(choices) > 0 {
						if choice, ok := choices[0].(map[string]interface{}); ok {
							if message, ok := choice["message"].(map[string]interface{}); ok {
								if content, ok := message["content"].([]interface{}); ok && len(content) > 0 {
									if contentItem, ok := content[0].(map[string]interface{}); ok {
										if text, ok := contentItem["text"].(string); ok {
											// 解析text中的JSON
											if err3 := json.Unmarshal([]byte(text), &qwenData); err3 != nil {
												log.Printf("Failed to parse nested JSON for ID %d: %v", id, err3)
												continue
											}
										}
									}
								}
							}
						}
					}
				}
			}
			
			if qwenData.QuestionType == "" {
				log.Printf("Failed to extract qwen data for ID %d", id)
				continue
			}
		}

		// 只保留有效的数据
		if qwenData.QuestionType != "" && qwenData.QuestionText != "" {
			testCase := TestCase{
				ID:           id,
				QuestionText: questionText,
				QwenData:     qwenData,
			}
			testCases = append(testCases, testCase)
		}
	}

	if err := rows.Err(); err != nil {
		log.Fatalf("Error iterating rows: %v", err)
	}

	fmt.Printf("总共查询到 %d 条记录\n", totalCount)
	fmt.Printf("成功解析 %d 条有效数据\n", len(testCases))

	// 将测试用例保存到JSON文件
	jsonData, err := json.MarshalIndent(testCases, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal test cases: %v", err)
	}

	if err := os.WriteFile("qwen_test_cases.json", jsonData, 0644); err != nil {
		log.Fatalf("Failed to write test cases file: %v", err)
	}

	fmt.Printf("测试用例已保存到 qwen_test_cases.json 文件\n")

	// 显示前几个示例
	fmt.Println("\n前5个测试用例示例:")
	fmt.Println(strings.Repeat("=", 80))

	for i, testCase := range testCases {
		if i >= 5 {
			break
		}

		fmt.Printf("\n测试用例 %d (ID: %d):\n", i+1, testCase.ID)
		fmt.Printf("数据库中的question_text: %s\n", testCase.QuestionText)
		fmt.Printf("Qwen原始question_text: %s\n", testCase.QwenData.QuestionText)
		fmt.Printf("Question_type: %s\n", testCase.QwenData.QuestionType)
		fmt.Printf("Question_num: %s\n", testCase.QwenData.QuestionNum)
		fmt.Println(strings.Repeat("-", 60))
	}
}
