package main

import (
	"fmt"
	"strings"
)

// 复制新的清洗逻辑进行测试
func CleanQuestionText(questionText, questionType, questionNum string) string {
	if questionText == "" {
		return ""
	}

	// 1. 生成基于question_num的序号模式
	numberPatterns := generateNumberPatterns(questionNum)

	// 2. 生成基于question_type的题目类型模式
	typePatterns := generateTypePatterns(questionType)

	// 3. 组合模式并进行清洗
	cleaned := questionText

	// 优先匹配完整模式：题目类型 + 序号
	for _, typePattern := range typePatterns {
		for _, numberPattern := range numberPatterns {
			combinedPattern := typePattern + numberPattern
			if strings.HasPrefix(cleaned, combinedPattern) {
				cleaned = strings.TrimPrefix(cleaned, combinedPattern)
				return strings.TrimSpace(cleaned)
			}
		}
	}

	// 特殊处理：匹配不完整的题目类型模式（如"断题)08、"）
	if len(questionType) > 1 {
		suffix := questionType[1:] // 如"判断题" -> "断题"
		for _, numberPattern := range numberPatterns {
			// 检查各种不完整模式
			patterns := []string{
				suffix + ")" + numberPattern,
				suffix + "）" + numberPattern,
			}

			for _, pattern := range patterns {
				// 直接匹配模式
				if strings.HasPrefix(cleaned, pattern) {
					cleaned = strings.TrimPrefix(cleaned, pattern)
					return strings.TrimSpace(cleaned)
				}

				// 检查是否以任意单字符开头，然后跟着模式
				if len(cleaned) > len(pattern) {
					// 尝试匹配 "X断题)08、" 的形式，其中X是任意字符
					if strings.HasPrefix(cleaned[1:], pattern) {
						cleaned = strings.TrimPrefix(cleaned, cleaned[:1+len(pattern)])
						return strings.TrimSpace(cleaned)
					}
				}
			}
		}
	}

	// 其次匹配仅序号模式
	for _, numberPattern := range numberPatterns {
		if strings.HasPrefix(cleaned, numberPattern) {
			cleaned = strings.TrimPrefix(cleaned, numberPattern)
			return strings.TrimSpace(cleaned)
		}
	}

	// 如果都没匹配到，返回原文本（去除首尾空白）
	return strings.TrimSpace(cleaned)
}

// generateNumberPatterns 生成基于question_num的序号模式
func generateNumberPatterns(questionNum string) []string {
	if questionNum == "" {
		return []string{}
	}

	patterns := []string{}

	// 原始序号模式
	patterns = append(patterns, []string{
		questionNum + ".",
		questionNum + "、",
		questionNum + "。",
		questionNum + " ",
		questionNum + "  ", // 两个空格的情况
	}...)

	// 处理带前导零的情况（如"8" -> "08"）
	if len(questionNum) == 1 {
		paddedNum := "0" + questionNum
		patterns = append(patterns, []string{
			paddedNum + ".",
			paddedNum + "、",
			paddedNum + "。",
			paddedNum + " ",
			paddedNum + "  ", // 两个空格的情况
		}...)
	}

	// 处理去掉前导零的情况（如"08" -> "8"）
	if len(questionNum) == 2 && questionNum[0] == '0' {
		trimmedNum := questionNum[1:]
		patterns = append(patterns, []string{
			trimmedNum + ".",
			trimmedNum + "、",
			trimmedNum + "。",
			trimmedNum + " ",
			trimmedNum + "  ", // 两个空格的情况
		}...)
	}

	return patterns
}

// generateTypePatterns 生成基于question_type的题目类型模式
func generateTypePatterns(questionType string) []string {
	if questionType == "" {
		return []string{}
	}
	
	patterns := []string{}
	
	// 完整的题目类型模式
	patterns = append(patterns, []string{
		questionType,
		"(" + questionType + ")",
		"（" + questionType + "）",
		questionType + ")",
		questionType + "）",
		"(" + questionType,
		"（" + questionType,
	}...)
	
	// 处理不完整的题目类型（如"断题"代替"判断题"）
	if len(questionType) > 1 {
		// 去掉第一个字的情况，如"断题"
		incompleteType := questionType[1:]
		patterns = append(patterns, []string{
			incompleteType + ")",
			incompleteType + "）",
			"*" + incompleteType + ")",
			"*" + incompleteType + "）",
		}...)
	}
	
	// 只有"题"字的情况
	patterns = append(patterns, []string{
		"题)",
		"题）",
	}...)
	
	return patterns
}

func main() {
	// 测试用例，基于S7.md和FormatQwenData.md的示例
	testCases := []struct {
		desc         string
		questionText string
		questionType string
		questionNum  string
		expected     string
	}{
		{
			desc:         "完整格式：(判断题)08、",
			questionText: "(判断题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "完整格式：(判断题)08.",
			questionText: "(判断题)08.等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "仅序号：08.",
			questionText: "08.等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "仅序号：08、",
			questionText: "08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "不完整类型：断题)08、",
			questionText: "断题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "无括号类型：判断题08、",
			questionText: "判断题08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "仅题字：题)08、",
			questionText: "题)08、等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "8",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "多选题测试：(多选题)15.雾天跟车行驶",
			questionText: "(多选题)15.雾天跟车行驶,应如何安全驾驶?",
			questionType: "多选题",
			questionNum:  "15",
			expected:     "雾天跟车行驶,应如何安全驾驶?",
		},
		{
			desc:         "单选题测试：(单选题)01、驾驶机动车",
			questionText: "(单选题)01、驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
			questionType: "单选题",
			questionNum:  "1",
			expected:     "驾驶机动车进入高速公路加速车道后，须尽快将车速提高到60公里每小时以上的原因是什么？",
		},
		{
			desc:         "正常题目（无需清洗）",
			questionText: "雾天跟车行驶,应如何安全驾驶?",
			questionType: "多选题",
			questionNum:  "15",
			expected:     "雾天跟车行驶,应如何安全驾驶?",
		},
		{
			desc:         "空格情况：7 等17分钟",
			questionText: "7 等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "7",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
		{
			desc:         "双空格情况：7  等17分钟",
			questionText: "7  等17分钟后如果没问题就可以走了。",
			questionType: "判断题",
			questionNum:  "7",
			expected:     "等17分钟后如果没问题就可以走了。",
		},
	}

	fmt.Println("新的精确清洗逻辑测试结果:")
	fmt.Println(strings.Repeat("=", 80))
	
	successCount := 0
	totalCount := len(testCases)
	
	for i, testCase := range testCases {
		fmt.Printf("\n测试 %d: %s\n", i+1, testCase.desc)
		fmt.Printf("原文: %s\n", testCase.questionText)
		fmt.Printf("类型: %s, 序号: %s\n", testCase.questionType, testCase.questionNum)
		fmt.Printf("期望: %s\n", testCase.expected)
		
		result := CleanQuestionText(testCase.questionText, testCase.questionType, testCase.questionNum)
		fmt.Printf("结果: %s\n", result)
		
		if result == testCase.expected {
			fmt.Printf("✅ 通过\n")
			successCount++
		} else {
			fmt.Printf("❌ 失败\n")
		}
		
		fmt.Println(strings.Repeat("-", 60))
	}
	
	fmt.Printf("\n" + strings.Repeat("=", 80))
	fmt.Printf("\n测试总结: %d/%d 通过 (%.1f%%)\n", successCount, totalCount, float64(successCount)/float64(totalCount)*100)
	
	if successCount == totalCount {
		fmt.Println("🎉 所有测试通过！新的清洗逻辑工作正常。")
	} else {
		fmt.Printf("⚠️  有 %d 个测试失败，需要进一步优化。\n", totalCount-successCount)
	}
}
